import 'dart:math';

import 'package:neuroworld/modules/auth/data/models/active_subscription.dart';
import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:neuroworld/modules/auth/data/sources/auth_shared_prefs.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'subscription_controller.g.dart';

@riverpod
class SubscriptionController extends _$SubscriptionController {
  @override
  FutureOr<bool?> build() => null;

  /// Updates the user's subscription status after successful purchase
  /// Implements simplified subscription logic:
  /// 1. If user doesn't have hasActiveSubscription and ActiveSubscription is null → create sample object and set hasActiveSubscription to true
  /// 2. If user has hasActiveSubscription and ActiveSubscription is not null → don't update these objects
  /// 3. Purchase should always complete regardless of existing subscription status
  Future<void> updateSubscriptionStatus(bool hasActiveSubscription,
      {String? productId}) async {
    state = const AsyncLoading();

    state = await AsyncValue.guard(() async {
      // Get current user
      final currentUser = ref.read(authStateProvider)?.user;
      if (currentUser == null) {
        throw Exception('No user found');
      }

      // Case 1: User doesn't have active subscription OR ActiveSubscription is null
      // → Create sample object and set hasActiveSubscription to true
      if (!currentUser.hasActiveSubscription ||
          currentUser.activeSubscription == null) {
        ActiveSubscription? activeSubscription;

        if (productId != null) {
          try {
            activeSubscription =
                _createActiveSubscriptionFromProductId(productId);
          } catch (e, stackTrace) {
            print("❌ Error creating ActiveSubscription: $e");
            print("Stack trace: $stackTrace");
            // Continue without activeSubscription - user will still get hasActiveSubscription: true
          }
        } else {
          print("ProductId is null, not creating ActiveSubscription");
        }

        final updatedUser = currentUser.copyWith(
          hasActiveSubscription: true,
          activeSubscription: activeSubscription,
        );

        // Update the auth state with the new user data (in-memory)
        ref.read(authStateProvider.notifier).updateUser(updatedUser);

        final currentAuth = ref.read(authStateProvider);
        if (currentAuth != null) {
          final updatedAuth = currentAuth.copyWith(user: updatedUser);
          try {
            await ref.read(authSharedPrefsProvider).setAuthData(updatedAuth);
          } catch (e) {
            print("❌ Error persisting to local storage: $e");
          }
        } else {
          print("❌ Current auth is null, cannot persist to local storage");
        }
      }

      return currentUser.hasActiveSubscription || hasActiveSubscription;
    });
  }

  /// Checks if user is trying to purchase an already purchased subscription
  /// Returns true if the subscription is already purchased, false otherwise
  bool isSubscriptionAlreadyPurchased(String productId) {
    final currentUser = ref.read(authStateProvider)?.user;
    if (currentUser == null ||
        !currentUser.hasActiveSubscription ||
        currentUser.activeSubscription == null) {
      return false;
    }

    final activeSubscription = currentUser.activeSubscription!;
    return activeSubscription.plan.productId == productId;
  }

  /// Refreshes subscription status from server
  Future<void> refreshSubscriptionStatus() async {
    state = const AsyncLoading();

    state = await AsyncValue.guard(() async {
      final authService = ref.read(authServiceProvider);
      final refreshedAuth = await authService.refreshUser();
      ref.read(authStateProvider.notifier).login(refreshedAuth);

      return refreshedAuth.user.hasActiveSubscription;
    });
  }

  /// Creates an ActiveSubscription object from a product ID
  ActiveSubscription _createActiveSubscriptionFromProductId(String productId) {
    final now = DateTime.now();

    final subscriptionInfo = _parseProductId(productId);

    // Generate random IDs (in a real app, these would come from the server)
    final subscriptionId = _generateRandomId();
    final planId = _generateRandomId();

    // Calculate expiry time based on billing period
    final expiryTime = subscriptionInfo.billingPeriod == 'monthly'
        ? now.add(const Duration(days: 30))
        : now.add(const Duration(days: 365));

    final plan = Plan(
      id: planId,
      tier: subscriptionInfo.tier,
      name: subscriptionInfo.name,
      billingPeriod: subscriptionInfo.billingPeriod,
      productId: productId,
      durationDays: subscriptionInfo.durationDays,
    );

    return ActiveSubscription(
      id: subscriptionId,
      plan: plan,
      status: 'active',
      startTime: now,
      expiryTime: expiryTime,
    );
  }

  ({String tier, String name, String billingPeriod, int durationDays})
      _parseProductId(String productId) {
    final parts = productId.split('.');
    if (parts.length < 5) {
      print("DEBUG: Invalid product ID format: $productId");
    }

    final tier = parts[3];
    final period = parts[4];

    final normalizedTier =
        (tier == 'academy' || tier == 'habit') ? 'habits' : tier;

    final tierName = _capitalizeTier(normalizedTier);
    final periodName = period == 'monthly' ? 'Monthly' : 'Yearly';
    final name = '$tierName $periodName';

    final durationDays = period == 'monthly' ? 30 : 365;

    return (
      tier: normalizedTier,
      name: name,
      billingPeriod: period,
      durationDays: durationDays,
    );
  }

  String _capitalizeTier(String tier) {
    switch (tier.toLowerCase()) {
      case 'habits':
        return 'Habits';
      case 'summit':
        return 'Summit';
      case 'clinical':
        return 'Clinical';
      default:
        return tier[0].toUpperCase() + tier.substring(1);
    }
  }

  String _generateRandomId() {
    final random = Random();
    const chars = 'abcdef0123456789';

    String generateSegment(int length) {
      return List.generate(
          length, (index) => chars[random.nextInt(chars.length)]).join();
    }

    return '${generateSegment(8)}-${generateSegment(4)}-${generateSegment(4)}-${generateSegment(4)}-${generateSegment(12)}';
  }
}
