import 'dart:io';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:neuroworld/modules/subscriptions/data/models/purchase_status.dart'
    as models;
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'purchase_service.g.dart';

@Riverpod(keepAlive: true)
PurchaseService purchaseService(Ref ref) => PurchaseService();

class PurchaseService {
  static const Set<String> productIds = {
    'com.neuroworld.dev.habit.monthly',
    'com.neuroworld.dev.habit.yearly',
    'com.neuroworld.dev.academy.monthly',
    'com.neuroworld.dev.academy.yearly',
    'com.neuroworld.dev.summit.monthly',
    'com.neuroworld.dev.summit.yearly',
    'com.neuroworld.dev.clinical.monthly',
    'com.neuroworld.dev.clinical.yearly',
  };

  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  bool _isStoreKit2Initialized = false;
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;

    // Enable StoreKit2 for iOS
    if (Platform.isIOS) {
      await _initializeStoreKit2();
    }

    _isInitialized = true;
  }

  Future<void> _initializeStoreKit2() async {
    try {
      _inAppPurchase
          .getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();

      _isStoreKit2Initialized = true;
    } catch (e) {
      _isStoreKit2Initialized = false;
    }
  }

  Future<List<ProductDetails>> loadProducts() async {
    await initialize();

    final bool isAvailable = await _inAppPurchase.isAvailable();
    if (!isAvailable) {
      throw Exception('Store not available');
    }

    final ProductDetailsResponse response =
        await _inAppPurchase.queryProductDetails(productIds.toSet());

    if (response.error != null) {
      throw Exception('Failed to load products: ${response.error}');
    }
    return response.productDetails;
  }

  Future<bool> purchaseProduct(String productId,
      {String? applicationUserName}) async {
    await initialize();

    final products = await loadProducts();
    final product = products.where((p) => p.id == productId).firstOrNull;

    if (product == null) {
      throw Exception('Product not found: $productId');
    }

    final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: product, applicationUserName: applicationUserName);

    final success =
        await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
    return success;
  }

  void listenToPurchaseUpdates() {
    _inAppPurchase.purchaseStream.listen(
      (List<PurchaseDetails> purchaseDetailsList) {
        for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
          // Handle different purchase statuses
          switch (purchaseDetails.status) {
            case PurchaseStatus.purchased:
              print("PurchaseStatus.purchased");
              _inAppPurchase.completePurchase(purchaseDetails);
              break;
            case PurchaseStatus.restored:
              print("PurchaseStatus.restored");
              _inAppPurchase.completePurchase(purchaseDetails);
              break;
            case PurchaseStatus.error:
              print("PurchaseStatus.error");
              _inAppPurchase.completePurchase(purchaseDetails);
              break;
            case PurchaseStatus.canceled:
              print("PurchaseStatus.canceled");
              _inAppPurchase.completePurchase(purchaseDetails);
              break;
            case PurchaseStatus.pending:
              print("PurchaseStatus.pending");
              _inAppPurchase.completePurchase(purchaseDetails);
              break;
          }
        }
      },
      onError: (error) {
        // Handle purchase stream errors silently
      },
    );
  }

  Future<ProductDetails?> getProduct(String productId) async {
    final products = await loadProducts();
    return products.where((p) => p.id == productId).firstOrNull;
  }

  // Expose purchase stream for UI to listen to purchase updates
  Stream<List<PurchaseDetails>> get purchaseStream =>
      _inAppPurchase.purchaseStream;

  bool get isStoreKit2Enabled => _isStoreKit2Initialized;

  models.PaymentSystem get paymentSystem {
    if (Platform.isIOS) {
      return _isStoreKit2Initialized
          ? models.PaymentSystem.storeKit2
          : models.PaymentSystem.storeKit1;
    }
    return models.PaymentSystem.googlePlayBilling;
  }

  models.StoreType get storeType {
    if (Platform.isIOS) {
      return models.StoreType.appStore;
    } else if (Platform.isAndroid) {
      return models.StoreType.googlePlay;
    }
    return models.StoreType.unknown;
  }

  String get platformInfo {
    if (Platform.isIOS) {
      return _isStoreKit2Initialized ? 'StoreKit2' : 'StoreKit1';
    }
    return 'Google Play Billing';
  }
}
