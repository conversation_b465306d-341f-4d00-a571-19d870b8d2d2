import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/rounded_icon.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/subscriptions/data/models/subscription_color_scheme.dart';

enum DrawerItem {
  profile,
  susbcription,
  feedback,
  badges,
  deleteAccount,
  logout;

  const DrawerItem();

  Widget get icon =>
      RoundedIcon(size: 33, child: SvgPicture.asset(_getDrawerIconPath()));

  Widget? getTrailing(WidgetRef ref) =>
      this == DrawerItem.susbcription ? _buildSubscriptionTrailing(ref) : null;

  Widget _buildSubscriptionTrailing(WidgetRef ref) {
    final user = ref.watch(authStateProvider)?.user;

    // Determine subscription tier and display text
    String tierText = "Free";
    Color backgroundColor = AppColors.subscriptionFree;

    if (user?.hasActiveSubscription == true &&
        user?.activeSubscription != null) {
      final tier = user!.activeSubscription!.plan.tier;

      // Capitalize the tier name for display
      tierText = _capitalizeTierName(tier);

      // Get color scheme for the tier
      final colorScheme = SubscriptionColorScheme.fromString(tier);
      backgroundColor = colorScheme?.primaryColor ?? AppColors.subscriptionFree;
    }

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Text(
        tierText,
        style: TextStyles.buttonSmall.copyWith(
          fontWeight: FontWeight.w600,
          letterSpacing: -0.3,
          color: Colors.white,
        ),
      ),
    );
  }

  String _capitalizeTierName(String tier) {
    switch (tier.toLowerCase()) {
      case 'habits':
        return 'Habits';
      case 'summit':
        return 'Summit';
      case 'clinical':
        return 'Clinical';
      case 'free':
        return 'Free';
      default:
        return tier[0].toUpperCase() + tier.substring(1);
    }
  }

  String getDrawerItemLabel(BuildContext context) {
    return switch (this) {
      DrawerItem.profile => context.L.menuItemProfile,
      DrawerItem.susbcription => context.L.menuItemSubscription,
      DrawerItem.feedback => context.L.menuItemFeedback,
      DrawerItem.badges => context.L.badges,
      DrawerItem.deleteAccount => context.L.menuItemDeleteAccount,
      DrawerItem.logout => context.L.menuItemLogout,
    };
  }

  String _getDrawerIconPath() {
    return switch (this) {
      DrawerItem.profile => Assets.svgs.menuDrawer.profile.path,
      DrawerItem.susbcription => Assets.svgs.menuDrawer.subscription.path,
      DrawerItem.feedback => Assets.svgs.menuDrawer.feedback.path,
      DrawerItem.badges => Assets.svgs.menuDrawer.badges.path,
      DrawerItem.deleteAccount => Assets.svgs.menuDrawer.deleteAccount.path,
      DrawerItem.logout => Assets.svgs.menuDrawer.logout.path,
    };
  }
}
